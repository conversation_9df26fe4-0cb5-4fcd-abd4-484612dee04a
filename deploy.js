const { Client } = require("ssh2");
const path = require("path");
// 服务器配置
const config = {
  host: "*************",
  port: 22,
  username: "root",
  password: "P@ssword2024",
  zipPath: path.join(__dirname, "./dist.zip"), // 本地ZIP路径
  remoteDir: "/opt/www/mzt/test/",
};

// 客户端初始化
const conn = new Client();

conn.on("ready", () => {
  console.log("*************环境，连接建立成功");
  const distDir = path.join(config.remoteDir, "dist").replace(/\\/g, "/");
  const remotePath = path
    .join(config.remoteDir, path.basename(config.zipPath))
    .replace(/\\/g, "/");

  // 删除dist目录下所有文件
  conn.exec(`find ${distDir} -type f -exec rm {} \;`, (err, rmStream) => {
    if (err) throw err;
  });
  conn.sftp((err, sftp) => {
    if (err) throw err;

    // 文件上传
    sftp.fastPut(config.zipPath, remotePath, {}, (err) => {
      if (err) {
        console.error("文件上传失败:", err);
        conn.end();
        return;
      }
      console.log(`文件已上传至 ${remotePath}`);

      // 解压操作
      conn.exec(
        `unzip -o ${remotePath} -d ${config.remoteDir}`,
        (err, stream) => {
          if (err) throw err;
          stream.on("exit", (code) => {
            console.log(code === 0 ? "解压完成" : `解压异常退出码: ${code}`);
            if (code === 0) {
              // 删除远程压缩包
              conn.exec(`rm -rf ${remotePath}`, (err, rmStream) => {
                if (err) throw err;
                rmStream.on("exit", (rmCode) => {
                  console.log(
                    rmCode === 0
                      ? "压缩包删除成功"
                      : `压缩包删除失败，退出码: ${code}`
                  );
                  conn.end();
                });
              });
              return true;
            }
            conn.end();
          });
          stream.stderr.on("data", (data) => {
            console.error("解压错误:", data.toString());
          });
        }
      );
    });
  });
});

// 错误处理
conn.on("error", (err) => {
  console.error("*************环境，连接错误:", err.message);
});

conn.on("end", () => {
  console.log("*************环境，连接已关闭");
});

// 执行连接
console.log("正在建立*************环境连接...");

conn.connect(config);
