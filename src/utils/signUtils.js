import CryptoJS from 'crypto-js'

/**
 * 签名工具类 - 前端版本
 * 对应后端 SignUtils 的签名生成功能
 */
export class SignUtils {
  /**
   * 请求头常量
   */
  private static readonly HEADERS = {
    ZS_ACCESS_ID: 'Zs-Access-Id',
    ZS_TIMESTAMP: 'Zs-Timestamp',
    ZS_NONCE: 'Zs-Nonce',
    ZS_SIGNATURE: 'Zs-Signature'
  } as const

  /**
   * 使用 HMAC-SHA256 算法生成签名
   * @param secret 密钥
   * @param content 待签名内容
   * @returns Base64编码的签名
   */
  public static hmacSHA256(secret: string, content: string): string {
    try {
      const hmac = CryptoJS.HmacSHA256(content, secret)
      return CryptoJS.enc.Base64.stringify(hmac)
    } catch (error) {
      console.error('hmacSHA256 error:', error)
      return ''
    }
  }

  /**
   * 生成随机字符串
   * @param length 字符串长度
   * @returns 随机字符串
   */
  public static generateNonce(length: number = 16): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  /**
   * 获取当前时间戳
   * @returns 毫秒级时间戳字符串
   */
  public static getCurrentTimestamp(): string {
    return Date.now().toString()
  }

  /**
   * 将对象转换为排序后的查询字符串
   * @param params 参数对象
   * @returns 排序后的查询字符串
   */
  public static sortJoinParams(params: Record<string, any>): string {
    const sortedKeys = Object.keys(params).sort()
    return sortedKeys
      .map(key => {
        const value = params[key]
        return `${key}=${value}`
      })
      .join('&')
  }

  /**
   * 构建请求参数字符串
   * @param headers 请求头
   * @param params URL参数
   * @param body 请求体
   * @returns 排序后的参数字符串
   */
  public static buildRequestParams(
    headers: Record<string, string>,
    params: Record<string, any> = {},
    body: Record<string, any> = {}
  ): string {
    const allParams: Record<string, any> = {}

    // 添加签名相关的请求头
    const signHeaders = [
      this.HEADERS.ZS_ACCESS_ID,
      this.HEADERS.ZS_TIMESTAMP,
      this.HEADERS.ZS_NONCE
    ]

    signHeaders.forEach(headerName => {
      if (headers[headerName]) {
        allParams[headerName] = headers[headerName]
      }
    })

    // 添加URL参数
    Object.assign(allParams, params)

    // 添加请求体参数
    Object.assign(allParams, body)

    return this.sortJoinParams(allParams)
  }

  /**
   * 生成签名
   * @param accessSecret 访问密钥
   * @param requestParams 请求参数字符串
   * @returns 签名
   */
  public static generateSignature(accessSecret: string, requestParams: string): string {
    return this.hmacSHA256(accessSecret, requestParams)
  }

  /**
   * 创建签名请求头
   * @param accessId 访问ID
   * @param accessSecret 访问密钥
   * @param params URL参数
   * @param body 请求体
   * @returns 包含签名的请求头
   */
  public static createSignedHeaders(
    accessId: string,
    accessSecret: string,
    params: Record<string, any> = {},
    body: Record<string, any> = {}
  ): Record<string, string> {
    const timestamp = this.getCurrentTimestamp()
    const nonce = this.generateNonce()

    const headers: Record<string, string> = {
      [this.HEADERS.ZS_ACCESS_ID]: accessId,
      [this.HEADERS.ZS_TIMESTAMP]: timestamp,
      [this.HEADERS.ZS_NONCE]: nonce
    }

    // 构建请求参数
    const requestParams = this.buildRequestParams(headers, params, body)

    // 生成签名
    const signature = this.generateSignature(accessSecret, requestParams)

    // 添加签名到请求头
    headers[this.HEADERS.ZS_SIGNATURE] = signature

    return headers
  }

  /**
   * 验证时间戳是否在有效期内（前后5分钟）
   * @param timestamp 时间戳
   * @param toleranceMinutes 容差分钟数，默认5分钟
   * @returns 是否有效
   */
  public static isValidTimestamp(timestamp: string, toleranceMinutes: number = 5): boolean {
    const currentTime = Date.now()
    const requestTime = parseInt(timestamp)
    const toleranceMs = toleranceMinutes * 60 * 1000

    return Math.abs(currentTime - requestTime) <= toleranceMs
  }
}

/**
 * 签名配置接口
 */
export interface SignConfig {
  accessId: string
  accessSecret: string
}

/**
 * 请求配置接口
 */
export interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  params?: Record<string, any>
  body?: Record<string, any>
  headers?: Record<string, string>
}

/**
 * 带签名的HTTP请求工具类
 */
export class SignedHttpClient {
  private config: SignConfig

  constructor(config: SignConfig) {
    this.config = config
  }

  /**
   * 发送带签名的HTTP请求
   * @param config 请求配置
   * @returns Promise<T>
   */
  async request<T = any>(config: RequestConfig): Promise<T> {
    const { url, method = 'GET', params = {}, body = {}, headers = {} } = config

    // 创建签名请求头
    const signedHeaders = SignUtils.createSignedHeaders(
      this.config.accessId,
      this.config.accessSecret,
      params,
      body
    )

    // 合并请求头
    const finalHeaders = {
      'Content-Type': 'application/json',
      ...headers,
      ...signedHeaders
    }

    // 构建请求选项
    const requestOptions: RequestInit = {
      method,
      headers: finalHeaders
    }

    // 添加请求体
    if (method !== 'GET' && Object.keys(body).length > 0) {
      requestOptions.body = JSON.stringify(body)
    }

    // 构建URL（添加查询参数）
    let finalUrl = url
    if (Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        searchParams.append(key, String(value))
      })
      finalUrl += `?${searchParams.toString()}`
    }

    try {
      const response = await fetch(finalUrl, requestOptions)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Signed request failed:', error)
      throw error
    }
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, params?: Record<string, any>, headers?: Record<string, string>): Promise<T> {
    return this.request<T>({ url, method: 'GET', params, headers })
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, body?: Record<string, any>, headers?: Record<string, string>): Promise<T> {
    return this.request<T>({ url, method: 'POST', body, headers })
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, body?: Record<string, any>, headers?: Record<string, string>): Promise<T> {
    return this.request<T>({ url, method: 'PUT', body, headers })
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, headers?: Record<string, string>): Promise<T> {
    return this.request<T>({ url, method: 'DELETE', headers })
  }
}

/**
 * Vue3 Composition API 签名工具
 */
export function useSignUtils() {
  /**
   * 创建签名客户端
   */
  const createSignedClient = (config: SignConfig) => {
    return new SignedHttpClient(config)
  }

  /**
   * 生成签名请求头
   */
  const generateSignedHeaders = (
    accessId: string,
    accessSecret: string,
    params: Record<string, any> = {},
    body: Record<string, any> = {}
  ) => {
    return SignUtils.createSignedHeaders(accessId, accessSecret, params, body)
  }

  /**
   * 验证时间戳
   */
  const validateTimestamp = (timestamp: string, toleranceMinutes: number = 5) => {
    return SignUtils.isValidTimestamp(timestamp, toleranceMinutes)
  }

  return {
    createSignedClient,
    generateSignedHeaders,
    validateTimestamp,
    SignUtils
  }
}

// 导出默认实例
export default SignUtils
