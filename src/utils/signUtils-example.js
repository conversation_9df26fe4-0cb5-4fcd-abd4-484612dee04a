import { SignUtils, SignedHttpClient, useSignUtils } from './signUtils.js'

/**
 * 签名工具使用示例
 */

// 示例配置
const config = {
  accessId: 'your-access-id',
  accessSecret: 'your-access-secret'
}

// ========== 基础签名工具使用示例 ==========

// 1. 生成签名请求头
function generateSignedHeadersExample() {
  const params = { page: 1, size: 10 }
  const body = { name: 'test', type: 'example' }
  
  const signedHeaders = SignUtils.createSignedHeaders(
    config.accessId,
    config.accessSecret,
    params,
    body
  )
  
  console.log('签名请求头:', signedHeaders)
  /*
  输出示例:
  {
    'Zs-Access-Id': 'your-access-id',
    'Zs-Timestamp': '1703123456789',
    'Zs-Nonce': 'abc123def456',
    'Zs-Signature': 'base64-encoded-signature'
  }
  */
  
  return signedHeaders
}

// 2. 验证时间戳
function validateTimestampExample() {
  const timestamp = Date.now().toString()
  const isValid = SignUtils.isValidTimestamp(timestamp)
  console.log('时间戳是否有效:', isValid)
  
  // 测试过期时间戳
  const oldTimestamp = (Date.now() - 10 * 60 * 1000).toString() // 10分钟前
  const isOldValid = SignUtils.isValidTimestamp(oldTimestamp)
  console.log('过期时间戳是否有效:', isOldValid)
}

// ========== HTTP客户端使用示例 ==========

// 3. 使用签名HTTP客户端
async function httpClientExample() {
  const client = new SignedHttpClient(config)
  
  try {
    // GET请求
    const getResponse = await client.get('/api/users', { page: 1, size: 10 })
    console.log('GET响应:', getResponse)
    
    // POST请求
    const postResponse = await client.post('/api/users', {
      name: 'John Doe',
      email: '<EMAIL>'
    })
    console.log('POST响应:', postResponse)
    
    // PUT请求
    const putResponse = await client.put('/api/users/1', {
      name: 'Jane Doe',
      email: '<EMAIL>'
    })
    console.log('PUT响应:', putResponse)
    
    // DELETE请求
    const deleteResponse = await client.delete('/api/users/1')
    console.log('DELETE响应:', deleteResponse)
    
  } catch (error) {
    console.error('请求失败:', error)
  }
}

// ========== Vue3 Composition API 使用示例 ==========

// 4. 在Vue3组件中使用
function vueCompositionExample() {
  const {
    createSignedClient,
    generateSignedHeaders,
    validateTimestamp,
    SignUtils: utils
  } = useSignUtils()
  
  // 创建客户端
  const client = createSignedClient(config)
  
  // 生成签名头
  const headers = generateSignedHeaders(
    config.accessId,
    config.accessSecret,
    { page: 1 },
    { name: 'test' }
  )
  
  // 验证时间戳
  const isValid = validateTimestamp(Date.now().toString())
  
  console.log('Vue组合式API示例:', { client, headers, isValid })
  
  return { client, headers, isValid }
}

// ========== 手动构建请求示例 ==========

// 5. 手动构建带签名的fetch请求
async function manualFetchExample() {
  const url = '/api/data'
  const params = { filter: 'active' }
  const body = { action: 'query' }
  
  // 生成签名头
  const signedHeaders = SignUtils.createSignedHeaders(
    config.accessId,
    config.accessSecret,
    params,
    body
  )
  
  // 构建完整URL
  const searchParams = new URLSearchParams(params)
  const fullUrl = `${url}?${searchParams.toString()}`
  
  try {
    const response = await fetch(fullUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...signedHeaders
      },
      body: JSON.stringify(body)
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('手动fetch响应:', data)
    return data
    
  } catch (error) {
    console.error('手动fetch失败:', error)
    throw error
  }
}

// ========== 错误处理示例 ==========

// 6. 错误处理和重试机制
async function errorHandlingExample() {
  const client = new SignedHttpClient(config)
  
  const maxRetries = 3
  let retries = 0
  
  while (retries < maxRetries) {
    try {
      const response = await client.get('/api/unreliable-endpoint')
      console.log('请求成功:', response)
      return response
      
    } catch (error) {
      retries++
      console.warn(`请求失败，重试 ${retries}/${maxRetries}:`, error.message)
      
      if (retries >= maxRetries) {
        console.error('达到最大重试次数，请求最终失败')
        throw error
      }
      
      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * retries))
    }
  }
}

// ========== 批量请求示例 ==========

// 7. 批量请求处理
async function batchRequestExample() {
  const client = new SignedHttpClient(config)
  
  const urls = [
    '/api/users/1',
    '/api/users/2',
    '/api/users/3'
  ]
  
  try {
    // 并发请求
    const promises = urls.map(url => client.get(url))
    const responses = await Promise.all(promises)
    
    console.log('批量请求响应:', responses)
    return responses
    
  } catch (error) {
    console.error('批量请求失败:', error)
    
    // 逐个请求（降级处理）
    const results = []
    for (const url of urls) {
      try {
        const response = await client.get(url)
        results.push(response)
      } catch (err) {
        console.warn(`请求 ${url} 失败:`, err.message)
        results.push(null)
      }
    }
    
    return results
  }
}

// 导出示例函数
export {
  generateSignedHeadersExample,
  validateTimestampExample,
  httpClientExample,
  vueCompositionExample,
  manualFetchExample,
  errorHandlingExample,
  batchRequestExample
}

// 如果直接运行此文件，执行所有示例
if (typeof window === 'undefined') {
  // Node.js环境
  console.log('=== 签名工具示例 ===')
  generateSignedHeadersExample()
  validateTimestampExample()
  vueCompositionExample()
} else {
  // 浏览器环境
  console.log('签名工具已加载，可在控制台中调用示例函数')
}
